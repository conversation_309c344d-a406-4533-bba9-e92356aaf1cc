package cn.hydee.gateway;

import cn.hydee.starter.grey.springboot.lib.smooth.SmoothService;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import reactor.blockhound.BlockHound;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients
@EnableApolloConfig
public class GateWayApplication {

    static {
        SmoothService.enhanceShutdownApi();
    }

    public static void main(String[] args) {
      BlockHound.install(); // 开启阻塞检测

      System.setProperty("csp.sentinel.app.type", "1");
        SpringApplication.run(GateWayApplication.class, args);
    }
}
